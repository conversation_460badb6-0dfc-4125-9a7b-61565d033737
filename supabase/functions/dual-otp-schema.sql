-- =====================================================
-- DUAL OTP SYSTEM DATABASE SCHEMA
-- Extends existing WhatsApp OTP system to support SMS
-- =====================================================

-- Create enum for OTP delivery methods
DO $$ BEGIN
    CREATE TYPE otp_delivery_method AS ENUM ('sms', 'whatsapp');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create enum for OTP purposes
DO $$ BEGIN
    CREATE TYPE otp_purpose AS ENUM ('registration', 'login', 'phone_verification', 'password_reset');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- USER PREFERENCES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.user_otp_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    phone TEXT NOT NULL,
    preferred_method otp_delivery_method DEFAULT 'whatsapp',
    allow_sms_fallback BOOLEAN DEFAULT true,
    allow_whatsapp_fallback BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one preference per user
    UNIQUE(user_id)
);

-- =====================================================
-- UNIFIED OTP TRACKING TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.otp_delivery_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    delivery_method otp_delivery_method NOT NULL,
    purpose otp_purpose NOT NULL,
    otp_hash TEXT NOT NULL, -- Hashed OTP for security
    expires_at TIMESTAMPTZ NOT NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMPTZ,
    delivery_status TEXT DEFAULT 'pending', -- pending, sent, failed, expired
    delivery_response JSONB, -- Store API response for debugging
    fallback_used BOOLEAN DEFAULT false,
    original_method otp_delivery_method, -- Track if fallback was used
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Index for efficient lookups
    INDEX idx_otp_delivery_phone_purpose (phone, purpose, created_at),
    INDEX idx_otp_delivery_user_purpose (user_id, purpose, created_at),
    INDEX idx_otp_delivery_expires (expires_at)
);

-- =====================================================
-- RATE LIMITING TABLE (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.otp_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone TEXT NOT NULL,
    delivery_method otp_delivery_method NOT NULL,
    purpose otp_purpose NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMPTZ DEFAULT NOW(),
    window_duration INTERVAL DEFAULT '1 hour',
    max_requests INTEGER DEFAULT 3,
    blocked_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Composite unique constraint for rate limiting windows
    UNIQUE(phone, delivery_method, purpose, window_start)
);

-- =====================================================
-- SMS CONFIGURATION TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sms_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name TEXT UNIQUE NOT NULL,
    template_id TEXT NOT NULL, -- MSG91 template ID
    dlt_template_id TEXT NOT NULL, -- DLT registered template ID
    content_template TEXT NOT NULL, -- Template with placeholders
    purpose otp_purpose NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- DATABASE FUNCTIONS
-- =====================================================

-- Enhanced rate limiting function for dual OTP
CREATE OR REPLACE FUNCTION public.check_dual_otp_rate_limit(
    phone_number TEXT,
    method otp_delivery_method,
    purpose_type otp_purpose DEFAULT 'login'
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_count INTEGER;
    window_start_time TIMESTAMPTZ;
    max_allowed INTEGER := 3;
    window_duration INTERVAL := '1 hour';
BEGIN
    -- Get current window start (hourly windows)
    window_start_time := date_trunc('hour', NOW());
    
    -- Check current count for this phone/method/purpose in current window
    SELECT COALESCE(request_count, 0) INTO current_count
    FROM public.otp_rate_limits
    WHERE phone = phone_number
      AND delivery_method = method
      AND purpose = purpose_type
      AND window_start = window_start_time;
    
    -- If no record exists or count is below limit, allow request
    IF current_count IS NULL OR current_count < max_allowed THEN
        -- Upsert the rate limit record
        INSERT INTO public.otp_rate_limits (
            phone, delivery_method, purpose, request_count, 
            window_start, max_requests, window_duration
        ) VALUES (
            phone_number, method, purpose_type, 1,
            window_start_time, max_allowed, window_duration
        )
        ON CONFLICT (phone, delivery_method, purpose, window_start)
        DO UPDATE SET 
            request_count = otp_rate_limits.request_count + 1,
            updated_at = NOW();
        
        RETURN TRUE;
    END IF;
    
    -- Rate limit exceeded
    RETURN FALSE;
END;
$$;

-- Enhanced OTP validation function for dual system
CREATE OR REPLACE FUNCTION public.validate_dual_otp(
    phone_number TEXT,
    otp_input TEXT,
    method otp_delivery_method,
    purpose_type otp_purpose DEFAULT 'login'
) RETURNS TABLE(
    is_valid BOOLEAN,
    error_message TEXT,
    user_data JSONB,
    delivery_method otp_delivery_method
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    otp_record RECORD;
    user_record RECORD;
    hashed_input TEXT;
BEGIN
    -- Hash the input OTP (in production, use proper hashing)
    hashed_input := otp_input;
    
    -- Find the most recent valid OTP for this phone/method/purpose
    SELECT * INTO otp_record
    FROM public.otp_delivery_log
    WHERE phone = phone_number
      AND delivery_method = method
      AND purpose = purpose_type
      AND verified = false
      AND expires_at > NOW()
      AND attempts < max_attempts
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Check if OTP record exists
    IF otp_record IS NULL THEN
        RETURN QUERY SELECT false, 'Invalid or expired OTP'::TEXT, NULL::JSONB, method;
        RETURN;
    END IF;
    
    -- Increment attempt count
    UPDATE public.otp_delivery_log
    SET attempts = attempts + 1, updated_at = NOW()
    WHERE id = otp_record.id;
    
    -- Check if OTP matches
    IF otp_record.otp_hash != hashed_input THEN
        -- Check if max attempts reached
        IF otp_record.attempts + 1 >= otp_record.max_attempts THEN
            UPDATE public.otp_delivery_log
            SET delivery_status = 'expired', updated_at = NOW()
            WHERE id = otp_record.id;
        END IF;
        
        RETURN QUERY SELECT false, 'Invalid OTP'::TEXT, NULL::JSONB, method;
        RETURN;
    END IF;
    
    -- OTP is valid, mark as verified
    UPDATE public.otp_delivery_log
    SET verified = true, verified_at = NOW(), delivery_status = 'verified', updated_at = NOW()
    WHERE id = otp_record.id;
    
    -- Get user data based on purpose
    IF purpose_type = 'registration' THEN
        -- For registration, get data from pending_whatsapp_users (legacy table)
        SELECT * INTO user_record
        FROM public.pending_whatsapp_users
        WHERE phone = phone_number;
        
        IF user_record IS NOT NULL THEN
            RETURN QUERY SELECT 
                true, 
                'OTP verified successfully'::TEXT,
                jsonb_build_object(
                    'full_name', user_record.full_name,
                    'password_hash', user_record.password_hash,
                    'phone', user_record.phone
                ),
                method;
        ELSE
            RETURN QUERY SELECT false, 'User data not found'::TEXT, NULL::JSONB, method;
        END IF;
    ELSE
        -- For login/verification, get data from profiles
        SELECT * INTO user_record
        FROM public.profiles
        WHERE phone = phone_number AND phone_verified = true;
        
        IF user_record IS NOT NULL THEN
            RETURN QUERY SELECT 
                true, 
                'OTP verified successfully'::TEXT,
                jsonb_build_object(
                    'user_id', user_record.id,
                    'full_name', user_record.full_name,
                    'phone', user_record.phone,
                    'email', user_record.email
                ),
                method;
        ELSE
            RETURN QUERY SELECT false, 'User not found or phone not verified'::TEXT, NULL::JSONB, method;
        END IF;
    END IF;
END;
$$;

-- Function to get user's preferred OTP method
CREATE OR REPLACE FUNCTION public.get_user_otp_preference(
    phone_number TEXT
) RETURNS TABLE(
    preferred_method otp_delivery_method,
    allow_sms_fallback BOOLEAN,
    allow_whatsapp_fallback BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    pref_record RECORD;
    profile_record RECORD;
BEGIN
    -- First try to get preference from user_otp_preferences
    SELECT * INTO pref_record
    FROM public.user_otp_preferences uop
    JOIN public.profiles p ON p.id = uop.user_id
    WHERE p.phone = phone_number;
    
    IF pref_record IS NOT NULL THEN
        RETURN QUERY SELECT 
            pref_record.preferred_method,
            pref_record.allow_sms_fallback,
            pref_record.allow_whatsapp_fallback;
        RETURN;
    END IF;
    
    -- If no preference set, return default (WhatsApp with SMS fallback)
    RETURN QUERY SELECT 
        'whatsapp'::otp_delivery_method,
        true,
        true;
END;
$$;

-- =====================================================
-- INDEXES AND CONSTRAINTS
-- =====================================================

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_otp_preferences_phone ON public.user_otp_preferences(phone);
CREATE INDEX IF NOT EXISTS idx_otp_delivery_log_lookup ON public.otp_delivery_log(phone, delivery_method, purpose, verified, expires_at);
CREATE INDEX IF NOT EXISTS idx_otp_rate_limits_lookup ON public.otp_rate_limits(phone, delivery_method, purpose, window_start);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE public.user_otp_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.otp_delivery_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.otp_rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sms_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_otp_preferences
CREATE POLICY "Users can view their own OTP preferences" ON public.user_otp_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own OTP preferences" ON public.user_otp_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own OTP preferences" ON public.user_otp_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for otp_delivery_log (admin and service role access)
CREATE POLICY "Service role can manage OTP delivery log" ON public.otp_delivery_log
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for sms_templates (admin access only)
CREATE POLICY "Service role can manage SMS templates" ON public.sms_templates
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to service role
GRANT ALL ON public.user_otp_preferences TO service_role;
GRANT ALL ON public.otp_delivery_log TO service_role;
GRANT ALL ON public.otp_rate_limits TO service_role;
GRANT ALL ON public.sms_templates TO service_role;

-- Grant permissions to authenticated users for their own data
GRANT SELECT, INSERT, UPDATE ON public.user_otp_preferences TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.check_dual_otp_rate_limit(TEXT, otp_delivery_method, otp_purpose) TO service_role;
GRANT EXECUTE ON FUNCTION public.validate_dual_otp(TEXT, TEXT, otp_delivery_method, otp_purpose) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_user_otp_preference(TEXT) TO service_role;

-- =====================================================
-- INSERT DEFAULT SMS TEMPLATES
-- =====================================================

INSERT INTO public.sms_templates (template_name, template_id, dlt_template_id, content_template, purpose) VALUES
('grid2play_registration_sms', 'grid2play_registration_sms', 'DLT_REG_TEMPLATE_ID', 'Your Grid2Play registration OTP is {otp}. Valid for 5 minutes. Do not share this code.', 'registration'),
('grid2play_login_sms', 'grid2play_login_sms', 'DLT_LOGIN_TEMPLATE_ID', 'Your Grid2Play login OTP is {otp}. Valid for 5 minutes. Do not share this code.', 'login'),
('grid2play_verification_sms', 'grid2play_verification_sms', 'DLT_VERIFY_TEMPLATE_ID', 'Your Grid2Play phone verification OTP is {otp}. Valid for 5 minutes.', 'phone_verification'),
('grid2play_password_reset_sms', 'grid2play_password_reset_sms', 'DLT_RESET_TEMPLATE_ID', 'Your Grid2Play password reset OTP is {otp}. Valid for 5 minutes.', 'password_reset')
ON CONFLICT (template_name) DO NOTHING;

-- =====================================================
-- MIGRATION NOTES
-- =====================================================

-- This schema extends the existing WhatsApp OTP system without breaking changes
-- Existing tables (pending_whatsapp_users, phone_otps) are preserved for compatibility
-- New unified system can gradually replace legacy tables
-- Rate limiting is enhanced but maintains backward compatibility
