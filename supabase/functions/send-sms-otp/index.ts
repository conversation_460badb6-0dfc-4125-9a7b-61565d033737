import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 SMS configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const senderId = Deno.env.get('MSG91_SENDER_ID') || 'GRID2P'
    const route = Deno.env.get('MSG91_SMS_ROUTE') || '4' // Transactional route

    if (!authKey) {
      console.error('MSG91 SMS configuration missing')
      return new Response(
        JSON.stringify({ error: 'SMS configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const { phone, full_name, password, isPhoneVerification, userId, isLogin, purpose = 'login' } = await req.json()

    if (!phone) {
      return new Response(
        JSON.stringify({ error: 'Phone number is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    if (!phoneRegex.test(cleanPhone)) {
      return new Response(
        JSON.stringify({ error: 'Invalid phone number format' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Determine OTP purpose
    let otpPurpose = 'login'
    if (isPhoneVerification) {
      otpPurpose = 'phone_verification'
    } else if (!isLogin && !isPhoneVerification) {
      otpPurpose = 'registration'
    }

    // Check rate limiting using enhanced database function
    const { data: rateLimitCheck, error: rateLimitError } = await supabaseAdmin
      .rpc('check_dual_otp_rate_limit', { 
        phone_number: cleanPhone,
        method: 'sms',
        purpose_type: otpPurpose
      })

    if (rateLimitError) {
      console.error('Rate limit check error:', rateLimitError)
      return new Response(
        JSON.stringify({ error: 'Rate limit check failed' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!rateLimitCheck) {
      return new Response(
        JSON.stringify({ error: 'Too many SMS OTP requests. Please try again later.' }),
        { 
          status: 429, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000) // 5 minutes

    console.log('Generated SMS OTP for phone:', cleanPhone, 'Purpose:', otpPurpose)

    // Get SMS template for the purpose
    const { data: smsTemplate, error: templateError } = await supabaseAdmin
      .from('sms_templates')
      .select('*')
      .eq('purpose', otpPurpose)
      .eq('is_active', true)
      .single()

    if (templateError || !smsTemplate) {
      console.error('SMS template not found for purpose:', otpPurpose, templateError)
      return new Response(
        JSON.stringify({ error: 'SMS template configuration error' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Store OTP in unified tracking table
    const { error: otpStoreError } = await supabaseAdmin
      .from('otp_delivery_log')
      .insert({
        phone: cleanPhone,
        user_id: userId || null,
        delivery_method: 'sms',
        purpose: otpPurpose,
        otp_hash: otp, // In production, this should be hashed
        expires_at: otpExpiresAt.toISOString(),
        attempts: 0,
        max_attempts: 3,
        verified: false,
        delivery_status: 'pending'
      })

    if (otpStoreError) {
      console.error('Error storing SMS OTP data:', otpStoreError)
      return new Response(
        JSON.stringify({ error: 'Failed to store OTP data' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Also store in legacy table for backward compatibility (if needed)
    if (!isPhoneVerification && !isLogin) {
      // For registration, store in pending_whatsapp_users for compatibility
      const { error: legacyError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .upsert({
          phone: cleanPhone,
          full_name: full_name?.trim() || 'User',
          password_hash: password || 'temp_password',
          otp_code: otp,
          otp_expires_at: otpExpiresAt.toISOString(),
          attempts: 0,
          last_otp_sent_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          purpose: 'registration'
        }, {
          onConflict: 'phone'
        })

      if (legacyError) {
        console.error('Error storing legacy OTP data:', legacyError)
        // Don't fail the request, just log the error
      }
    }

    // Prepare SMS content
    const smsContent = smsTemplate.content_template.replace('{otp}', otp)

    // Prepare MSG91 SMS API payload
    const msg91Payload = {
      sender: senderId,
      route: route,
      country: '91', // India country code
      sms: [
        {
          message: smsContent,
          to: [cleanPhone.replace('+', '')]
        }
      ]
    }

    console.log('Sending SMS OTP via MSG91:', { 
      phone: cleanPhone, 
      template: smsTemplate.template_name,
      dlt_template_id: smsTemplate.dlt_template_id 
    })

    // Send SMS OTP via MSG91 API
    const response = await fetch('https://api.msg91.com/api/v5/sms/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error('MSG91 SMS API error:', responseData)
      
      // Update delivery status to failed
      await supabaseAdmin
        .from('otp_delivery_log')
        .update({
          delivery_status: 'failed',
          delivery_response: responseData,
          updated_at: new Date().toISOString()
        })
        .eq('phone', cleanPhone)
        .eq('delivery_method', 'sms')
        .eq('purpose', otpPurpose)
        .eq('verified', false)
        .order('created_at', { ascending: false })
        .limit(1)

      return new Response(
        JSON.stringify({ 
          success: false,
          error: `SMS delivery failed: ${response.status} ${response.statusText}`,
          details: responseData 
        }),
        { 
          status: response.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Update delivery status to sent
    await supabaseAdmin
      .from('otp_delivery_log')
      .update({
        delivery_status: 'sent',
        delivery_response: responseData,
        updated_at: new Date().toISOString()
      })
      .eq('phone', cleanPhone)
      .eq('delivery_method', 'sms')
      .eq('purpose', otpPurpose)
      .eq('verified', false)
      .order('created_at', { ascending: false })
      .limit(1)

    console.log('SMS OTP sent successfully via MSG91:', responseData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'SMS OTP sent successfully',
        phone: cleanPhone,
        delivery_method: 'sms',
        expires_in: 300, // 5 minutes in seconds
        template_used: smsTemplate.template_name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in send-sms-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
